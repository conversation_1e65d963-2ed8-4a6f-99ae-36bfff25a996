"""
Query Router
Route queries to specialized retrievers based on intent and domain classification
"""

from typing import Dict, List, Any, Optional, Tuple, Set
import logging
import asyncio
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import re
import json
from collections import defaultdict

class QueryDomain(Enum):
    """Query domains for specialized routing."""
    GENERAL = "general"
    TECHNICAL = "technical"
    BILLING = "billing"
    ACCOUNT = "account"
    PRODUCT = "product"
    SUPPORT = "support"
    FAQ = "faq"
    TROUBLESHOOTING = "troubleshooting"
    INTEGRATION = "integration"
    SECURITY = "security"

class QueryComplexity(Enum):
    """Query complexity levels."""
    SIMPLE = "simple"
    MODERATE = "moderate"
    COMPLEX = "complex"
    MULTI_STEP = "multi_step"

class RoutingStrategy(Enum):
    """Routing strategies for different scenarios."""
    SINGLE_BEST = "single_best"
    ENSEMBLE = "ensemble"
    HIERARCHICAL = "hierarchical"
    PARALLEL = "parallel"

@dataclass
class RoutingDecision:
    """Decision made by the query router."""
    
    primary_domain: QueryDomain
    secondary_domains: List[QueryDomain] = field(default_factory=list)
    complexity: QueryComplexity = QueryComplexity.SIMPLE
    strategy: RoutingStrategy = RoutingStrategy.SINGLE_BEST
    
    # Retriever assignments
    primary_retriever: str = ""
    secondary_retrievers: List[str] = field(default_factory=list)
    
    # Confidence and reasoning
    confidence: float = 1.0
    reasoning: str = ""
    
    # Routing metadata
    query_features: Dict[str, Any] = field(default_factory=dict)
    routing_time: datetime = field(default_factory=datetime.now)

@dataclass
class RetrieverConfig:
    """Configuration for a specialized retriever."""
    
    retriever_id: str
    name: str
    description: str
    domains: Set[QueryDomain] = field(default_factory=set)
    
    # Retriever capabilities
    supports_complex_queries: bool = True
    supports_multi_step: bool = False
    max_query_length: int = 1000
    
    # Performance characteristics
    avg_response_time: float = 1.0
    accuracy_score: float = 0.8
    coverage_score: float = 0.7
    
    # Resource requirements
    memory_usage: str = "medium"
    cpu_intensive: bool = False
    
    # Configuration
    active: bool = True
    priority: int = 1  # Higher number = higher priority

class QueryRouter:
    """Route queries to appropriate specialized retrievers."""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Routing settings
        self.routing_settings = {
            "default_strategy": RoutingStrategy.SINGLE_BEST,
            "ensemble_threshold": 0.7,
            "complexity_threshold": 0.6,
            "multi_step_threshold": 0.8,
            "min_confidence": 0.5
        }
        
        # Registered retrievers
        self.retrievers: Dict[str, RetrieverConfig] = {}
        
        # Domain classification patterns
        self.domain_patterns = self._initialize_domain_patterns()
        
        # Query analysis patterns
        self.complexity_patterns = self._initialize_complexity_patterns()
        
        # Initialize default retrievers
        asyncio.create_task(self._initialize_default_retrievers())
        
        # Routing statistics
        self.routing_stats = defaultdict(int)
    
    def _initialize_domain_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for domain classification."""
        
        return {
            "technical": [
                r"\b(api|sdk|integration|code|programming|development)\b",
                r"\b(error|bug|exception|stack trace|debug)\b",
                r"\b(configuration|setup|installation|deployment)\b",
                r"\b(database|server|network|security|authentication)\b"
            ],
            "billing": [
                r"\b(payment|billing|invoice|charge|subscription|plan)\b",
                r"\b(refund|credit|discount|pricing|cost|fee)\b",
                r"\b(upgrade|downgrade|cancel|renew)\b",
                r"\$\d+|\b\d+\s*(dollars?|usd|cents?)\b"
            ],
            "account": [
                r"\b(account|profile|settings|preferences|login|password)\b",
                r"\b(user|username|email|phone|address)\b",
                r"\b(access|permissions|roles|security)\b",
                r"\b(update|change|modify|edit)\b.*\b(account|profile)\b"
            ],
            "product": [
                r"\b(feature|functionality|capability|tool|service)\b",
                r"\b(how to|tutorial|guide|instructions|steps)\b",
                r"\b(use|using|usage|utilize|work with)\b",
                r"\b(dashboard|interface|ui|ux|design)\b"
            ],
            "troubleshooting": [
                r"\b(problem|issue|trouble|not working|broken|failed)\b",
                r"\b(fix|solve|resolve|repair|troubleshoot)\b",
                r"\b(help|assistance|support|stuck)\b",
                r"\b(why|what's wrong|what happened|error)\b"
            ],
            "faq": [
                r"\b(what is|what are|how do|how can|when should)\b",
                r"\b(frequently asked|common question|often asked)\b",
                r"\b(explain|definition|meaning|purpose)\b",
                r"^\s*(q:|question:)\s*"
            ],
            "integration": [
                r"\b(integrate|integration|connect|connection|sync)\b",
                r"\b(third party|external|webhook|api|plugin)\b",
                r"\b(import|export|data transfer|migration)\b",
                r"\b(crm|erp|salesforce|hubspot|slack)\b"
            ],
            "security": [
                r"\b(security|privacy|encryption|ssl|tls)\b",
                r"\b(authentication|authorization|oauth|sso)\b",
                r"\b(vulnerability|breach|attack|malware)\b",
                r"\b(compliance|gdpr|hipaa|pci|audit)\b"
            ]
        }
    
    def _initialize_complexity_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for complexity assessment."""
        
        return {
            "simple": [
                r"^\s*what is\b",
                r"^\s*how do i\b",
                r"^\s*can i\b",
                r"^\s*where is\b"
            ],
            "moderate": [
                r"\b(configure|setup|implement|customize)\b",
                r"\b(compare|difference|versus|vs)\b",
                r"\b(best practice|recommendation|suggest)\b",
                r"\?\s*.*\?\s*"  # Multiple questions
            ],
            "complex": [
                r"\b(architecture|design|strategy|approach)\b",
                r"\b(optimize|performance|scalability|efficiency)\b",
                r"\b(troubleshoot|diagnose|analyze|investigate)\b",
                r"\b(if.*then|depending on|based on|considering)\b"
            ],
            "multi_step": [
                r"\b(first.*then|step.*step|process|workflow)\b",
                r"\b(after.*before|sequence|order|procedure)\b",
                r"\b(multiple|several|various|different)\b.*\b(steps|phases|stages)\b",
                r"\band then\b.*\band then\b"
            ]
        }
    
    async def _initialize_default_retrievers(self):
        """Initialize default specialized retrievers."""
        
        try:
            # General purpose retriever
            self.register_retriever(RetrieverConfig(
                retriever_id="general",
                name="General Purpose Retriever",
                description="Handles general queries across all domains",
                domains={QueryDomain.GENERAL},
                supports_complex_queries=True,
                supports_multi_step=True,
                accuracy_score=0.8,
                coverage_score=0.9,
                priority=1
            ))
            
            # Technical documentation retriever
            self.register_retriever(RetrieverConfig(
                retriever_id="technical",
                name="Technical Documentation Retriever",
                description="Specialized for technical queries and API documentation",
                domains={QueryDomain.TECHNICAL, QueryDomain.INTEGRATION},
                supports_complex_queries=True,
                supports_multi_step=True,
                accuracy_score=0.9,
                coverage_score=0.7,
                priority=3
            ))
            
            # FAQ retriever
            self.register_retriever(RetrieverConfig(
                retriever_id="faq",
                name="FAQ Retriever",
                description="Optimized for frequently asked questions",
                domains={QueryDomain.FAQ},
                supports_complex_queries=False,
                supports_multi_step=False,
                max_query_length=200,
                accuracy_score=0.95,
                coverage_score=0.6,
                avg_response_time=0.5,
                priority=2
            ))
            
            # Billing and account retriever
            self.register_retriever(RetrieverConfig(
                retriever_id="billing_account",
                name="Billing & Account Retriever",
                description="Handles billing and account-related queries",
                domains={QueryDomain.BILLING, QueryDomain.ACCOUNT},
                supports_complex_queries=True,
                supports_multi_step=False,
                accuracy_score=0.85,
                coverage_score=0.8,
                priority=2
            ))
            
            # Troubleshooting retriever
            self.register_retriever(RetrieverConfig(
                retriever_id="troubleshooting",
                name="Troubleshooting Retriever",
                description="Specialized for problem-solving and troubleshooting",
                domains={QueryDomain.TROUBLESHOOTING, QueryDomain.SUPPORT},
                supports_complex_queries=True,
                supports_multi_step=True,
                accuracy_score=0.8,
                coverage_score=0.7,
                priority=2
            ))
            
            # Product features retriever
            self.register_retriever(RetrieverConfig(
                retriever_id="product",
                name="Product Features Retriever",
                description="Handles product features and usage queries",
                domains={QueryDomain.PRODUCT},
                supports_complex_queries=True,
                supports_multi_step=False,
                accuracy_score=0.85,
                coverage_score=0.8,
                priority=2
            ))
            
            self.logger.info("Default retrievers initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize default retrievers: {e}")
    
    def register_retriever(self, retriever_config: RetrieverConfig):
        """Register a new specialized retriever."""
        
        self.retrievers[retriever_config.retriever_id] = retriever_config
        self.logger.info(f"Retriever registered: {retriever_config.retriever_id} - {retriever_config.name}")
    
    def unregister_retriever(self, retriever_id: str) -> bool:
        """Unregister a retriever."""
        
        if retriever_id in self.retrievers:
            del self.retrievers[retriever_id]
            self.logger.info(f"Retriever unregistered: {retriever_id}")
            return True
        return False
    
    async def route_query(self, 
                         query: str, 
                         context: Optional[Dict[str, Any]] = None,
                         user_preferences: Optional[Dict[str, Any]] = None) -> RoutingDecision:
        """Route a query to appropriate retrievers."""
        
        try:
            # Analyze query characteristics
            domain = await self._classify_domain(query, context)
            complexity = await self._assess_complexity(query)
            
            # Determine routing strategy
            strategy = await self._determine_strategy(domain, complexity, context)
            
            # Select retrievers
            primary_retriever, secondary_retrievers = await self._select_retrievers(
                domain, complexity, strategy, user_preferences
            )
            
            # Calculate confidence
            confidence = await self._calculate_routing_confidence(
                query, domain, complexity, primary_retriever
            )
            
            # Generate reasoning
            reasoning = await self._generate_routing_reasoning(
                domain, complexity, strategy, primary_retriever
            )
            
            # Extract query features
            features = await self._extract_query_features(query)
            
            decision = RoutingDecision(
                primary_domain=domain,
                complexity=complexity,
                strategy=strategy,
                primary_retriever=primary_retriever,
                secondary_retrievers=secondary_retrievers,
                confidence=confidence,
                reasoning=reasoning,
                query_features=features
            )
            
            # Update statistics
            self.routing_stats[f"domain_{domain.value}"] += 1
            self.routing_stats[f"complexity_{complexity.value}"] += 1
            self.routing_stats[f"strategy_{strategy.value}"] += 1
            self.routing_stats[f"retriever_{primary_retriever}"] += 1
            
            return decision
            
        except Exception as e:
            self.logger.error(f"Query routing failed: {e}")
            return RoutingDecision(
                primary_domain=QueryDomain.GENERAL,
                complexity=QueryComplexity.SIMPLE,
                strategy=RoutingStrategy.SINGLE_BEST,
                primary_retriever="general",
                confidence=0.5,
                reasoning=f"Routing failed, using default: {e}"
            )

    async def _classify_domain(self, query: str, context: Optional[Dict[str, Any]]) -> QueryDomain:
        """Classify the query domain."""

        query_lower = query.lower()
        domain_scores = {}

        # Score each domain based on pattern matches
        for domain, patterns in self.domain_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query_lower))
                score += matches

            if score > 0:
                domain_scores[domain] = score

        # Consider context hints
        if context:
            context_domain = context.get("suggested_domain")
            if context_domain and context_domain in domain_scores:
                domain_scores[context_domain] += 1

        # Return highest scoring domain or default
        if domain_scores:
            best_domain = max(domain_scores.items(), key=lambda x: x[1])[0]
            return QueryDomain(best_domain)

        return QueryDomain.GENERAL

    async def _assess_complexity(self, query: str) -> QueryComplexity:
        """Assess query complexity."""

        query_lower = query.lower()
        complexity_scores = {}

        # Score complexity levels
        for complexity, patterns in self.complexity_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    score += 1

            if score > 0:
                complexity_scores[complexity] = score

        # Additional complexity indicators
        word_count = len(query.split())
        question_count = query.count('?')

        # Adjust scores based on length and structure
        if word_count > 50:
            complexity_scores["complex"] = complexity_scores.get("complex", 0) + 1
        elif word_count > 20:
            complexity_scores["moderate"] = complexity_scores.get("moderate", 0) + 1

        if question_count > 1:
            complexity_scores["multi_step"] = complexity_scores.get("multi_step", 0) + 1

        # Return highest scoring complexity or default
        if complexity_scores:
            best_complexity = max(complexity_scores.items(), key=lambda x: x[1])[0]
            return QueryComplexity(best_complexity)

        return QueryComplexity.SIMPLE

    async def _determine_strategy(self,
                                domain: QueryDomain,
                                complexity: QueryComplexity,
                                context: Optional[Dict[str, Any]]) -> RoutingStrategy:
        """Determine the best routing strategy."""

        # Multi-step queries benefit from hierarchical approach
        if complexity == QueryComplexity.MULTI_STEP:
            return RoutingStrategy.HIERARCHICAL

        # Complex queries might benefit from ensemble
        if complexity == QueryComplexity.COMPLEX:
            return RoutingStrategy.ENSEMBLE

        # Check if context suggests a specific strategy
        if context and context.get("preferred_strategy"):
            try:
                return RoutingStrategy(context["preferred_strategy"])
            except ValueError:
                pass

        # Default to single best retriever
        return RoutingStrategy.SINGLE_BEST

    async def _select_retrievers(self,
                               domain: QueryDomain,
                               complexity: QueryComplexity,
                               strategy: RoutingStrategy,
                               user_preferences: Optional[Dict[str, Any]]) -> Tuple[str, List[str]]:
        """Select appropriate retrievers based on analysis."""

        # Find retrievers that support the domain
        candidate_retrievers = []

        for retriever_id, config in self.retrievers.items():
            if not config.active:
                continue

            # Check domain support
            if domain in config.domains or QueryDomain.GENERAL in config.domains:
                # Check complexity support
                if complexity == QueryComplexity.MULTI_STEP and not config.supports_multi_step:
                    continue
                if complexity == QueryComplexity.COMPLEX and not config.supports_complex_queries:
                    continue

                candidate_retrievers.append((retriever_id, config))

        if not candidate_retrievers:
            # Fallback to general retriever
            return "general", []

        # Sort by priority and accuracy
        candidate_retrievers.sort(key=lambda x: (x[1].priority, x[1].accuracy_score), reverse=True)

        # Select based on strategy
        if strategy == RoutingStrategy.SINGLE_BEST:
            primary = candidate_retrievers[0][0]
            return primary, []

        elif strategy == RoutingStrategy.ENSEMBLE:
            # Use top 2-3 retrievers
            primary = candidate_retrievers[0][0]
            secondary = [r[0] for r in candidate_retrievers[1:3]]
            return primary, secondary

        elif strategy == RoutingStrategy.HIERARCHICAL:
            # Use specialized retriever first, then general
            specialized = [r for r in candidate_retrievers if r[1].retriever_id != "general"]
            if specialized:
                primary = specialized[0][0]
                secondary = ["general"] if "general" in self.retrievers else []
                return primary, secondary
            else:
                return "general", []

        elif strategy == RoutingStrategy.PARALLEL:
            # Use multiple retrievers in parallel
            primary = candidate_retrievers[0][0]
            secondary = [r[0] for r in candidate_retrievers[1:]]
            return primary, secondary

        # Default fallback
        return candidate_retrievers[0][0], []

    async def _calculate_routing_confidence(self,
                                          query: str,
                                          domain: QueryDomain,
                                          complexity: QueryComplexity,
                                          primary_retriever: str) -> float:
        """Calculate confidence in routing decision."""

        confidence = 0.5  # Base confidence

        # Domain classification confidence
        query_lower = query.lower()
        domain_patterns = self.domain_patterns.get(domain.value, [])
        domain_matches = sum(1 for pattern in domain_patterns
                           if re.search(pattern, query_lower))

        if domain_matches > 0:
            confidence += min(0.3, domain_matches * 0.1)

        # Retriever suitability confidence
        if primary_retriever in self.retrievers:
            retriever_config = self.retrievers[primary_retriever]
            if domain in retriever_config.domains:
                confidence += 0.2

            # Complexity match
            if complexity == QueryComplexity.MULTI_STEP and retriever_config.supports_multi_step:
                confidence += 0.1
            elif complexity == QueryComplexity.COMPLEX and retriever_config.supports_complex_queries:
                confidence += 0.1

        return min(1.0, confidence)

    async def _generate_routing_reasoning(self,
                                        domain: QueryDomain,
                                        complexity: QueryComplexity,
                                        strategy: RoutingStrategy,
                                        primary_retriever: str) -> str:
        """Generate human-readable reasoning for routing decision."""

        reasoning_parts = []

        # Domain reasoning
        reasoning_parts.append(f"Classified as {domain.value} domain")

        # Complexity reasoning
        reasoning_parts.append(f"assessed as {complexity.value} complexity")

        # Strategy reasoning
        strategy_reasons = {
            RoutingStrategy.SINGLE_BEST: "using single best retriever for efficiency",
            RoutingStrategy.ENSEMBLE: "using ensemble approach for better coverage",
            RoutingStrategy.HIERARCHICAL: "using hierarchical approach for multi-step processing",
            RoutingStrategy.PARALLEL: "using parallel retrievers for comprehensive results"
        }
        reasoning_parts.append(strategy_reasons.get(strategy, "using default strategy"))

        # Retriever reasoning
        if primary_retriever in self.retrievers:
            retriever_name = self.retrievers[primary_retriever].name
            reasoning_parts.append(f"selected {retriever_name} as primary retriever")

        return "; ".join(reasoning_parts)

    async def _extract_query_features(self, query: str) -> Dict[str, Any]:
        """Extract features from the query for analysis."""

        features = {
            "length": len(query),
            "word_count": len(query.split()),
            "question_count": query.count('?'),
            "has_technical_terms": bool(re.search(r'\b(api|sdk|json|xml|http|ssl)\b', query.lower())),
            "has_numbers": bool(re.search(r'\d+', query)),
            "has_urls": bool(re.search(r'https?://', query)),
            "has_code": bool(re.search(r'[{}()\[\]<>]', query)),
            "sentiment": "neutral"  # Placeholder for sentiment analysis
        }

        return features

    async def get_retriever_recommendations(self,
                                          query: str,
                                          context: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Get ranked list of retriever recommendations."""

        domain = await self._classify_domain(query, context)
        complexity = await self._assess_complexity(query)

        recommendations = []

        for retriever_id, config in self.retrievers.items():
            if not config.active:
                continue

            # Calculate suitability score
            score = 0.0

            # Domain match
            if domain in config.domains:
                score += 0.4
            elif QueryDomain.GENERAL in config.domains:
                score += 0.2

            # Complexity support
            if complexity == QueryComplexity.MULTI_STEP and config.supports_multi_step:
                score += 0.2
            elif complexity == QueryComplexity.COMPLEX and config.supports_complex_queries:
                score += 0.2
            elif complexity in [QueryComplexity.SIMPLE, QueryComplexity.MODERATE]:
                score += 0.1

            # Performance factors
            score += config.accuracy_score * 0.2
            score += config.coverage_score * 0.1
            score += (config.priority / 5.0) * 0.1

            recommendations.append({
                "retriever_id": retriever_id,
                "name": config.name,
                "description": config.description,
                "suitability_score": score,
                "accuracy": config.accuracy_score,
                "coverage": config.coverage_score,
                "avg_response_time": config.avg_response_time,
                "supports_complexity": {
                    "complex": config.supports_complex_queries,
                    "multi_step": config.supports_multi_step
                }
            })

        # Sort by suitability score
        recommendations.sort(key=lambda x: x["suitability_score"], reverse=True)

        return recommendations

    async def get_routing_statistics(self) -> Dict[str, Any]:
        """Get routing statistics and performance metrics."""

        total_routes = sum(self.routing_stats.values())

        if total_routes == 0:
            return {"message": "No routing statistics available"}

        stats = {
            "total_routes": total_routes,
            "domain_distribution": {},
            "complexity_distribution": {},
            "strategy_distribution": {},
            "retriever_usage": {},
            "active_retrievers": len([r for r in self.retrievers.values() if r.active]),
            "total_retrievers": len(self.retrievers)
        }

        # Calculate distributions
        for key, count in self.routing_stats.items():
            percentage = (count / total_routes) * 100

            if key.startswith("domain_"):
                domain = key.replace("domain_", "")
                stats["domain_distribution"][domain] = {
                    "count": count,
                    "percentage": round(percentage, 2)
                }
            elif key.startswith("complexity_"):
                complexity = key.replace("complexity_", "")
                stats["complexity_distribution"][complexity] = {
                    "count": count,
                    "percentage": round(percentage, 2)
                }
            elif key.startswith("strategy_"):
                strategy = key.replace("strategy_", "")
                stats["strategy_distribution"][strategy] = {
                    "count": count,
                    "percentage": round(percentage, 2)
                }
            elif key.startswith("retriever_"):
                retriever = key.replace("retriever_", "")
                stats["retriever_usage"][retriever] = {
                    "count": count,
                    "percentage": round(percentage, 2)
                }

        return stats

    async def get_retriever_status(self) -> List[Dict[str, Any]]:
        """Get status of all registered retrievers."""

        status_list = []

        for retriever_id, config in self.retrievers.items():
            status = {
                "retriever_id": retriever_id,
                "name": config.name,
                "description": config.description,
                "active": config.active,
                "domains": [d.value for d in config.domains],
                "capabilities": {
                    "supports_complex_queries": config.supports_complex_queries,
                    "supports_multi_step": config.supports_multi_step,
                    "max_query_length": config.max_query_length
                },
                "performance": {
                    "accuracy_score": config.accuracy_score,
                    "coverage_score": config.coverage_score,
                    "avg_response_time": config.avg_response_time
                },
                "priority": config.priority,
                "usage_count": self.routing_stats.get(f"retriever_{retriever_id}", 0)
            }
            status_list.append(status)

        # Sort by priority and usage
        status_list.sort(key=lambda x: (x["priority"], x["usage_count"]), reverse=True)

        return status_list

    def clear_statistics(self):
        """Clear routing statistics."""
        self.routing_stats.clear()
        self.logger.info("Routing statistics cleared")
