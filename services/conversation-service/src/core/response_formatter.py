"""
Response Formatter
Format responses based on content type and user preferences
"""

from typing import Dict, List, Any, Optional, Union
import logging
import re
import json
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import markdown
import html

class ResponseFormat(Enum):
    """Supported response formats."""
    PLAIN_TEXT = "plain_text"
    MARKDOWN = "markdown"
    HTML = "html"
    JSON = "json"
    STRUCTURED = "structured"

class ContentType(Enum):
    """Types of content that can be formatted."""
    STEP_BY_STEP = "step_by_step"
    BULLET_POINTS = "bullet_points"
    NUMBERED_LIST = "numbered_list"
    TABLE = "table"
    CODE_SNIPPET = "code_snippet"
    FAQ = "faq"
    COMPARISON = "comparison"
    TROUBLESHOOTING = "troubleshooting"
    EXPLANATION = "explanation"
    QUICK_ANSWER = "quick_answer"

@dataclass
class FormattingOptions:
    """Options for response formatting."""
    
    format_type: ResponseFormat = ResponseFormat.MARKDOWN
    content_type: ContentType = ContentType.EXPLANATION
    
    # Style preferences
    use_emojis: bool = False
    include_headers: bool = True
    include_summary: bool = False
    max_line_length: int = 80
    
    # Structure preferences
    add_numbering: bool = True
    add_bullet_points: bool = True
    highlight_important: bool = True
    include_code_blocks: bool = True
    
    # Customer preferences
    customer_tier: str = "standard"
    technical_level: str = "medium"  # basic, medium, advanced
    preferred_length: str = "medium"  # short, medium, long

@dataclass
class FormattedResponse:
    """Formatted response with metadata."""
    
    content: str
    format_type: ResponseFormat
    content_type: ContentType
    
    # Formatting metadata
    word_count: int
    estimated_read_time: int  # in seconds
    formatting_applied: List[str] = field(default_factory=list)
    
    # Alternative formats
    alternative_formats: Dict[str, str] = field(default_factory=dict)
    
    # Quality metrics
    readability_score: float = 0.0
    structure_score: float = 0.0

class ResponseFormatter:
    """Format responses based on content type and user preferences."""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Formatting templates
        self.templates = self._initialize_templates()
        
        # Formatting rules
        self.formatting_rules = self._initialize_formatting_rules()
        
        # Content type detectors
        self.content_detectors = self._initialize_content_detectors()
    
    def _initialize_templates(self) -> Dict[str, Dict[str, str]]:
        """Initialize formatting templates for different content types."""
        
        return {
            "step_by_step": {
                "markdown": "## {title}\n\n{steps}\n\n{conclusion}",
                "html": "<h2>{title}</h2>\n<ol>\n{steps}\n</ol>\n<p>{conclusion}</p>",
                "plain_text": "{title}\n\n{steps}\n\n{conclusion}"
            },
            "bullet_points": {
                "markdown": "## {title}\n\n{points}\n\n{summary}",
                "html": "<h2>{title}</h2>\n<ul>\n{points}\n</ul>\n<p>{summary}</p>",
                "plain_text": "{title}\n\n{points}\n\n{summary}"
            },
            "table": {
                "markdown": "## {title}\n\n{table}\n\n{notes}",
                "html": "<h2>{title}</h2>\n<table>\n{table}\n</table>\n<p>{notes}</p>",
                "plain_text": "{title}\n\n{table}\n\n{notes}"
            },
            "code_snippet": {
                "markdown": "## {title}\n\n```{language}\n{code}\n```\n\n{explanation}",
                "html": "<h2>{title}</h2>\n<pre><code class='{language}'>{code}</code></pre>\n<p>{explanation}</p>",
                "plain_text": "{title}\n\n{code}\n\n{explanation}"
            },
            "faq": {
                "markdown": "## {question}\n\n{answer}\n\n{related_links}",
                "html": "<h2>{question}</h2>\n<p>{answer}</p>\n<div>{related_links}</div>",
                "plain_text": "Q: {question}\n\nA: {answer}\n\n{related_links}"
            },
            "troubleshooting": {
                "markdown": "## Problem: {problem}\n\n### Solution\n{solution}\n\n### If this doesn't work\n{alternatives}",
                "html": "<h2>Problem: {problem}</h2>\n<h3>Solution</h3>\n<p>{solution}</p>\n<h3>If this doesn't work</h3>\n<p>{alternatives}</p>",
                "plain_text": "Problem: {problem}\n\nSolution:\n{solution}\n\nIf this doesn't work:\n{alternatives}"
            }
        }
    
    def _initialize_formatting_rules(self) -> Dict[str, Any]:
        """Initialize formatting rules for different scenarios."""
        
        return {
            "customer_tier": {
                "free": {
                    "max_length": 500,
                    "include_advanced_features": False,
                    "tone": "helpful"
                },
                "premium": {
                    "max_length": 1000,
                    "include_advanced_features": True,
                    "tone": "professional"
                },
                "enterprise": {
                    "max_length": 2000,
                    "include_advanced_features": True,
                    "tone": "technical"
                }
            },
            "technical_level": {
                "basic": {
                    "use_simple_language": True,
                    "include_explanations": True,
                    "avoid_jargon": True
                },
                "medium": {
                    "use_simple_language": False,
                    "include_explanations": True,
                    "avoid_jargon": False
                },
                "advanced": {
                    "use_simple_language": False,
                    "include_explanations": False,
                    "avoid_jargon": False
                }
            },
            "urgency": {
                "low": {
                    "include_background": True,
                    "detailed_explanation": True
                },
                "medium": {
                    "include_background": False,
                    "detailed_explanation": True
                },
                "high": {
                    "include_background": False,
                    "detailed_explanation": False,
                    "highlight_solution": True
                },
                "critical": {
                    "quick_answer_first": True,
                    "minimal_formatting": True
                }
            }
        }
    
    def _initialize_content_detectors(self) -> Dict[str, List[str]]:
        """Initialize patterns to detect content types."""
        
        return {
            "step_by_step": [
                r"\b(step|steps|first|second|third|then|next|finally)\b",
                r"\b(how to|tutorial|guide|instructions)\b",
                r"\d+\.\s+",
                r"(step \d+|phase \d+)"
            ],
            "bullet_points": [
                r"^\s*[-*•]\s+",
                r"\b(list|points|items|features)\b",
                r"(benefits|advantages|options)"
            ],
            "table": [
                r"\|.*\|",
                r"\b(comparison|vs|versus|table|chart)\b",
                r"(columns|rows|data)"
            ],
            "code_snippet": [
                r"```",
                r"\b(code|script|function|class|method)\b",
                r"(programming|syntax|api)"
            ],
            "troubleshooting": [
                r"\b(error|problem|issue|bug|fix|solve)\b",
                r"(troubleshoot|debug|resolve|repair)",
                r"(not working|broken|failed)"
            ],
            "faq": [
                r"\b(question|faq|frequently asked)\b",
                r"(what is|how do|why does|when should)",
                r"(q:|a:|question:|answer:)"
            ]
        }
    
    async def format_response(self, 
                            content: str, 
                            options: FormattingOptions) -> FormattedResponse:
        """Format response according to specified options."""
        
        try:
            # Detect content type if not specified
            if options.content_type == ContentType.EXPLANATION:
                detected_type = await self._detect_content_type(content)
                if detected_type:
                    options.content_type = detected_type
            
            # Apply formatting based on content type
            formatted_content = await self._apply_content_formatting(content, options)
            
            # Apply style formatting
            formatted_content = await self._apply_style_formatting(formatted_content, options)
            
            # Generate alternative formats
            alternatives = await self._generate_alternative_formats(formatted_content, options)
            
            # Calculate metrics
            word_count = len(formatted_content.split())
            read_time = max(1, word_count // 200)  # Assume 200 words per minute
            
            # Calculate quality scores
            readability_score = await self._calculate_readability_score(formatted_content)
            structure_score = await self._calculate_structure_score(formatted_content, options)
            
            return FormattedResponse(
                content=formatted_content,
                format_type=options.format_type,
                content_type=options.content_type,
                word_count=word_count,
                estimated_read_time=read_time,
                formatting_applied=self._get_applied_formatting(options),
                alternative_formats=alternatives,
                readability_score=readability_score,
                structure_score=structure_score
            )
            
        except Exception as e:
            self.logger.error(f"Response formatting failed: {e}")
            return FormattedResponse(
                content=content,
                format_type=ResponseFormat.PLAIN_TEXT,
                content_type=ContentType.EXPLANATION,
                word_count=len(content.split()),
                estimated_read_time=1
            )
    
    async def _detect_content_type(self, content: str) -> Optional[ContentType]:
        """Detect the most likely content type from the content."""
        
        content_lower = content.lower()
        type_scores = {}
        
        for content_type, patterns in self.content_detectors.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, content_lower))
                score += matches
            
            if score > 0:
                type_scores[content_type] = score
        
        if type_scores:
            best_type = max(type_scores.items(), key=lambda x: x[1])[0]
            return ContentType(best_type)
        
        return None
    
    async def _apply_content_formatting(self, 
                                      content: str, 
                                      options: FormattingOptions) -> str:
        """Apply content-type specific formatting."""
        
        content_type = options.content_type.value
        format_type = options.format_type.value
        
        # Get template for this content type and format
        template_group = self.templates.get(content_type, {})
        template = template_group.get(format_type, "{content}")
        
        # Parse content based on type
        if content_type == "step_by_step":
            return await self._format_step_by_step(content, template, options)
        elif content_type == "bullet_points":
            return await self._format_bullet_points(content, template, options)
        elif content_type == "table":
            return await self._format_table(content, template, options)
        elif content_type == "code_snippet":
            return await self._format_code_snippet(content, template, options)
        elif content_type == "faq":
            return await self._format_faq(content, template, options)
        elif content_type == "troubleshooting":
            return await self._format_troubleshooting(content, template, options)
        else:
            return content
    
    async def _format_step_by_step(self, 
                                 content: str, 
                                 template: str, 
                                 options: FormattingOptions) -> str:
        """Format step-by-step instructions."""
        
        # Extract title and steps
        lines = content.split('\n')
        title = lines[0] if lines else "Instructions"
        
        # Find step patterns
        steps = []
        current_step = ""
        
        for line in lines[1:]:
            if re.match(r'^\s*\d+\.', line) or re.match(r'^\s*(step|Step)', line):
                if current_step:
                    steps.append(current_step.strip())
                current_step = line
            else:
                current_step += f"\n{line}"
        
        if current_step:
            steps.append(current_step.strip())
        
        # Format steps based on output format
        if options.format_type == ResponseFormat.MARKDOWN:
            formatted_steps = '\n'.join([f"{i+1}. {step}" for i, step in enumerate(steps)])
        elif options.format_type == ResponseFormat.HTML:
            formatted_steps = '\n'.join([f"<li>{step}</li>" for step in steps])
        else:
            formatted_steps = '\n'.join([f"{i+1}. {step}" for i, step in enumerate(steps)])
        
        conclusion = "Follow these steps in order for the best results."
        
        return template.format(
            title=title,
            steps=formatted_steps,
            conclusion=conclusion
        )

    async def _format_bullet_points(self,
                                   content: str,
                                   template: str,
                                   options: FormattingOptions) -> str:
        """Format bullet point lists."""

        lines = content.split('\n')
        title = lines[0] if lines else "Key Points"

        # Extract bullet points
        points = []
        for line in lines[1:]:
            if re.match(r'^\s*[-*•]', line) or line.strip().startswith('-'):
                points.append(line.strip().lstrip('-*• '))
            elif line.strip() and not points:
                # If no bullet points found, treat each non-empty line as a point
                points.append(line.strip())

        # Format points based on output format
        if options.format_type == ResponseFormat.MARKDOWN:
            formatted_points = '\n'.join([f"- {point}" for point in points])
        elif options.format_type == ResponseFormat.HTML:
            formatted_points = '\n'.join([f"<li>{point}</li>" for point in points])
        else:
            formatted_points = '\n'.join([f"• {point}" for point in points])

        summary = f"These are the {len(points)} key points to remember."

        return template.format(
            title=title,
            points=formatted_points,
            summary=summary
        )

    async def _format_table(self,
                          content: str,
                          template: str,
                          options: FormattingOptions) -> str:
        """Format tabular data."""

        lines = content.split('\n')
        title = "Comparison Table"

        # Simple table detection and formatting
        table_lines = [line for line in lines if '|' in line]

        if options.format_type == ResponseFormat.MARKDOWN:
            formatted_table = '\n'.join(table_lines)
        elif options.format_type == ResponseFormat.HTML:
            # Convert to HTML table
            html_rows = []
            for i, line in enumerate(table_lines):
                cells = [cell.strip() for cell in line.split('|') if cell.strip()]
                if i == 0:
                    html_rows.append('<tr>' + ''.join([f'<th>{cell}</th>' for cell in cells]) + '</tr>')
                else:
                    html_rows.append('<tr>' + ''.join([f'<td>{cell}</td>' for cell in cells]) + '</tr>')
            formatted_table = '\n'.join(html_rows)
        else:
            formatted_table = '\n'.join(table_lines)

        notes = "Review the table for detailed comparisons."

        return template.format(
            title=title,
            table=formatted_table,
            notes=notes
        )

    async def _format_code_snippet(self,
                                 content: str,
                                 template: str,
                                 options: FormattingOptions) -> str:
        """Format code snippets."""

        # Extract code blocks
        code_pattern = r'```(\w+)?\n(.*?)\n```'
        code_matches = re.findall(code_pattern, content, re.DOTALL)

        if code_matches:
            language, code = code_matches[0]
            language = language or "text"
        else:
            # Look for indented code
            lines = content.split('\n')
            code_lines = [line for line in lines if line.startswith('    ') or line.startswith('\t')]
            if code_lines:
                code = '\n'.join([line.lstrip() for line in code_lines])
                language = "text"
            else:
                code = content
                language = "text"

        title = "Code Example"
        explanation = "Use this code snippet as a reference for your implementation."

        return template.format(
            title=title,
            language=language,
            code=code,
            explanation=explanation
        )

    async def _format_faq(self,
                        content: str,
                        template: str,
                        options: FormattingOptions) -> str:
        """Format FAQ content."""

        # Try to extract question and answer
        lines = content.split('\n')
        question = ""
        answer = ""

        for i, line in enumerate(lines):
            if re.match(r'^\s*(q:|question:)', line.lower()):
                question = line.split(':', 1)[1].strip()
            elif re.match(r'^\s*(a:|answer:)', line.lower()):
                answer = '\n'.join(lines[i:]).split(':', 1)[1].strip()
                break

        if not question:
            question = lines[0] if lines else "Frequently Asked Question"
        if not answer:
            answer = '\n'.join(lines[1:]) if len(lines) > 1 else content

        related_links = "For more information, check our documentation."

        return template.format(
            question=question,
            answer=answer,
            related_links=related_links
        )

    async def _format_troubleshooting(self,
                                    content: str,
                                    template: str,
                                    options: FormattingOptions) -> str:
        """Format troubleshooting content."""

        lines = content.split('\n')

        # Extract problem and solution
        problem = "Issue Description"
        solution = content
        alternatives = "If the problem persists, please contact support."

        # Look for problem/solution patterns
        for i, line in enumerate(lines):
            if re.search(r'\b(problem|issue|error)\b', line.lower()):
                problem = line
                solution = '\n'.join(lines[i+1:])
                break

        return template.format(
            problem=problem,
            solution=solution,
            alternatives=alternatives
        )

    async def _apply_style_formatting(self,
                                    content: str,
                                    options: FormattingOptions) -> str:
        """Apply style-based formatting."""

        formatted = content

        # Apply customer tier rules
        tier_rules = self.formatting_rules["customer_tier"].get(options.customer_tier, {})
        max_length = tier_rules.get("max_length", 1000)

        if len(formatted) > max_length:
            # Truncate and add continuation
            formatted = formatted[:max_length] + "\n\n[Content truncated - contact support for full details]"

        # Apply technical level adjustments
        tech_rules = self.formatting_rules["technical_level"].get(options.technical_level, {})

        if tech_rules.get("use_simple_language", False):
            # Replace technical terms with simpler alternatives
            replacements = {
                "authenticate": "log in",
                "configuration": "settings",
                "implementation": "setup",
                "optimization": "improvement"
            }
            for technical, simple in replacements.items():
                formatted = re.sub(r'\b' + technical + r'\b', simple, formatted, flags=re.IGNORECASE)

        # Add emojis if requested
        if options.use_emojis:
            formatted = self._add_emojis(formatted)

        # Highlight important information
        if options.highlight_important:
            formatted = self._highlight_important_info(formatted, options)

        return formatted

    def _add_emojis(self, content: str) -> str:
        """Add relevant emojis to content."""

        emoji_patterns = {
            r'\b(warning|caution|important)\b': '⚠️ ',
            r'\b(success|completed|done)\b': '✅ ',
            r'\b(error|failed|problem)\b': '❌ ',
            r'\b(tip|hint|suggestion)\b': '💡 ',
            r'\b(note|remember)\b': '📝 ',
            r'\b(step|steps)\b': '👉 '
        }

        for pattern, emoji in emoji_patterns.items():
            content = re.sub(pattern, emoji + r'\g<0>', content, flags=re.IGNORECASE)

        return content

    def _highlight_important_info(self, content: str, options: FormattingOptions) -> str:
        """Highlight important information based on format type."""

        if options.format_type == ResponseFormat.MARKDOWN:
            # Bold important terms
            important_patterns = [
                r'\b(important|critical|warning|note|tip)\b',
                r'\b(required|mandatory|must)\b',
                r'\b(error|failed|success)\b'
            ]

            for pattern in important_patterns:
                content = re.sub(pattern, r'**\g<0>**', content, flags=re.IGNORECASE)

        elif options.format_type == ResponseFormat.HTML:
            # Use strong tags
            important_patterns = [
                r'\b(important|critical|warning|note|tip)\b',
                r'\b(required|mandatory|must)\b',
                r'\b(error|failed|success)\b'
            ]

            for pattern in important_patterns:
                content = re.sub(pattern, r'<strong>\g<0></strong>', content, flags=re.IGNORECASE)

        return content

    async def _generate_alternative_formats(self,
                                          content: str,
                                          options: FormattingOptions) -> Dict[str, str]:
        """Generate alternative format versions of the content."""

        alternatives = {}

        try:
            # Generate plain text version
            if options.format_type != ResponseFormat.PLAIN_TEXT:
                plain_text = self._convert_to_plain_text(content)
                alternatives["plain_text"] = plain_text

            # Generate markdown version
            if options.format_type != ResponseFormat.MARKDOWN:
                markdown_content = self._convert_to_markdown(content, options)
                alternatives["markdown"] = markdown_content

            # Generate HTML version
            if options.format_type != ResponseFormat.HTML:
                html_content = self._convert_to_html(content, options)
                alternatives["html"] = html_content

            # Generate JSON version for API consumption
            json_content = self._convert_to_json(content, options)
            alternatives["json"] = json_content

        except Exception as e:
            self.logger.warning(f"Failed to generate alternative formats: {e}")

        return alternatives

    def _convert_to_plain_text(self, content: str) -> str:
        """Convert formatted content to plain text."""

        # Remove markdown formatting
        plain = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # Bold
        plain = re.sub(r'\*(.*?)\*', r'\1', plain)        # Italic
        plain = re.sub(r'`(.*?)`', r'\1', plain)          # Code
        plain = re.sub(r'#{1,6}\s+', '', plain)          # Headers
        plain = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', plain)  # Links

        # Remove HTML tags
        plain = re.sub(r'<[^>]+>', '', plain)

        return plain.strip()

    def _convert_to_markdown(self, content: str, options: FormattingOptions) -> str:
        """Convert content to markdown format."""

        if options.format_type == ResponseFormat.HTML:
            # Basic HTML to Markdown conversion
            md = content
            md = re.sub(r'<h([1-6])>(.*?)</h[1-6]>', r'\n' + r'#' * 1 + r' \2\n', md)
            md = re.sub(r'<strong>(.*?)</strong>', r'**\1**', md)
            md = re.sub(r'<em>(.*?)</em>', r'*\1*', md)
            md = re.sub(r'<code>(.*?)</code>', r'`\1`', md)
            md = re.sub(r'<li>(.*?)</li>', r'- \1', md)
            md = re.sub(r'<[^>]+>', '', md)  # Remove remaining tags
            return md.strip()

        return content

    def _convert_to_html(self, content: str, options: FormattingOptions) -> str:
        """Convert content to HTML format."""

        if options.format_type == ResponseFormat.MARKDOWN:
            # Use markdown library if available
            try:
                import markdown
                return markdown.markdown(content)
            except ImportError:
                # Basic markdown to HTML conversion
                html = content
                html = re.sub(r'^#{1,6}\s+(.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
                html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)
                html = re.sub(r'\*(.*?)\*', r'<em>\1</em>', html)
                html = re.sub(r'`(.*?)`', r'<code>\1</code>', html)
                html = re.sub(r'^- (.+)$', r'<li>\1</li>', html, flags=re.MULTILINE)
                html = html.replace('\n', '<br>\n')
                return html

        return f"<div>{html.escape(content)}</div>"

    def _convert_to_json(self, content: str, options: FormattingOptions) -> str:
        """Convert content to JSON format for API consumption."""

        json_data = {
            "content": content,
            "format": options.format_type.value,
            "content_type": options.content_type.value,
            "metadata": {
                "word_count": len(content.split()),
                "character_count": len(content),
                "estimated_read_time": max(1, len(content.split()) // 200)
            }
        }

        return json.dumps(json_data, indent=2)

    async def _calculate_readability_score(self, content: str) -> float:
        """Calculate readability score (simplified)."""

        words = content.split()
        sentences = re.split(r'[.!?]+', content)

        if not words or not sentences:
            return 0.0

        avg_words_per_sentence = len(words) / len(sentences)
        avg_syllables_per_word = sum(self._count_syllables(word) for word in words) / len(words)

        # Simplified Flesch Reading Ease formula
        score = 206.835 - (1.015 * avg_words_per_sentence) - (84.6 * avg_syllables_per_word)
        return max(0.0, min(100.0, score)) / 100.0

    def _count_syllables(self, word: str) -> int:
        """Count syllables in a word (simplified)."""

        word = word.lower()
        vowels = 'aeiouy'
        syllable_count = 0
        previous_was_vowel = False

        for char in word:
            if char in vowels:
                if not previous_was_vowel:
                    syllable_count += 1
                previous_was_vowel = True
            else:
                previous_was_vowel = False

        # Handle silent e
        if word.endswith('e'):
            syllable_count -= 1

        return max(1, syllable_count)

    async def _calculate_structure_score(self, content: str, options: FormattingOptions) -> float:
        """Calculate structure quality score."""

        score = 0.0

        # Check for headers
        if re.search(r'^#{1,6}\s+', content, re.MULTILINE) or re.search(r'<h[1-6]>', content):
            score += 0.3

        # Check for lists
        if re.search(r'^[-*•]\s+', content, re.MULTILINE) or re.search(r'<li>', content):
            score += 0.2

        # Check for code blocks
        if '```' in content or '<code>' in content:
            score += 0.2

        # Check for proper paragraph breaks
        paragraphs = content.split('\n\n')
        if len(paragraphs) > 1:
            score += 0.2

        # Check for emphasis
        if re.search(r'\*\*.*?\*\*', content) or re.search(r'<strong>', content):
            score += 0.1

        return min(1.0, score)

    def _get_applied_formatting(self, options: FormattingOptions) -> List[str]:
        """Get list of formatting features that were applied."""

        applied = []

        if options.include_headers:
            applied.append("headers")
        if options.add_numbering:
            applied.append("numbering")
        if options.add_bullet_points:
            applied.append("bullet_points")
        if options.use_emojis:
            applied.append("emojis")
        if options.highlight_important:
            applied.append("highlighting")
        if options.include_code_blocks:
            applied.append("code_blocks")

        applied.append(f"format_{options.format_type.value}")
        applied.append(f"content_{options.content_type.value}")

        return applied
