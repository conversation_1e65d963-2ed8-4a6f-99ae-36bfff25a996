"""
Consistency Validator
Ensure response consistency with policies, guidelines, and previous responses
"""

from typing import Dict, List, Any, Optional, Set, Tuple
import logging
import re
import asyncio
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json
from collections import defaultdict

class ConsistencyLevel(Enum):
    """Levels of consistency validation."""
    STRICT = "strict"
    MODERATE = "moderate"
    LENIENT = "lenient"

class ViolationType(Enum):
    """Types of consistency violations."""
    POLICY_VIOLATION = "policy_violation"
    GUIDELINE_DEVIATION = "guideline_deviation"
    FACTUAL_INCONSISTENCY = "factual_inconsistency"
    TONE_INCONSISTENCY = "tone_inconsistency"
    CONTRADICTORY_ADVICE = "contradictory_advice"
    OUTDATED_INFORMATION = "outdated_information"
    UNAUTHORIZED_CLAIM = "unauthorized_claim"

class ViolationSeverity(Enum):
    """Severity levels for violations."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"

@dataclass
class PolicyRule:
    """Individual policy rule for validation."""
    
    rule_id: str
    name: str
    description: str
    rule_type: str  # "must", "must_not", "should", "should_not"
    
    # Rule patterns
    patterns: List[str] = field(default_factory=list)
    keywords: Set[str] = field(default_factory=set)
    
    # Rule metadata
    category: str = "general"
    severity: ViolationSeverity = ViolationSeverity.MEDIUM
    active: bool = True
    
    # Context restrictions
    applies_to_domains: Set[str] = field(default_factory=set)
    customer_tiers: Set[str] = field(default_factory=set)
    
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

@dataclass
class ConsistencyViolation:
    """Detected consistency violation."""
    
    violation_id: str
    violation_type: ViolationType
    severity: ViolationSeverity
    
    # Violation details
    rule_violated: str
    description: str
    evidence: str
    suggested_fix: str = ""
    
    # Context
    response_segment: str = ""
    position_start: int = 0
    position_end: int = 0
    
    # Confidence
    confidence_score: float = 1.0
    
    detected_at: datetime = field(default_factory=datetime.now)

@dataclass
class ConsistencyReport:
    """Complete consistency validation report."""
    
    response_id: str
    response_content: str
    validation_level: ConsistencyLevel
    
    # Validation results
    is_consistent: bool = True
    overall_score: float = 1.0
    violations: List[ConsistencyViolation] = field(default_factory=list)
    
    # Metrics
    policy_compliance_score: float = 1.0
    factual_consistency_score: float = 1.0
    tone_consistency_score: float = 1.0
    
    # Recommendations
    requires_review: bool = False
    recommended_actions: List[str] = field(default_factory=list)
    
    # Metadata
    validation_time: datetime = field(default_factory=datetime.now)
    rules_checked: int = 0
    processing_time: float = 0.0

class ConsistencyValidator:
    """Validate response consistency with policies and guidelines."""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Validation settings
        self.validation_settings = {
            "default_level": ConsistencyLevel.MODERATE,
            "min_confidence_threshold": 0.7,
            "max_violations_before_rejection": 5,
            "critical_violation_threshold": 1
        }
        
        # Policy rules
        self.policy_rules: Dict[str, PolicyRule] = {}
        
        # Consistency patterns
        self.consistency_patterns = self._initialize_consistency_patterns()
        
        # Initialize default policies
        asyncio.create_task(self._initialize_default_policies())
        
        # Validation cache
        self.validation_cache: Dict[str, ConsistencyReport] = {}
    
    def _initialize_consistency_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for consistency checking."""
        
        return {
            "prohibited_claims": [
                r"\b(guarantee|guaranteed|100% certain|absolutely sure|never fails)\b",
                r"\b(always works|perfect solution|no risk|risk-free)\b",
                r"\b(best|only|exclusive|unique solution)\b"
            ],
            "medical_legal_advice": [
                r"\b(diagnose|diagnosis|medical advice|legal advice|lawsuit)\b",
                r"\b(you should sue|take legal action|medical treatment)\b",
                r"\b(prescription|medication|therapy|surgery)\b"
            ],
            "financial_advice": [
                r"\b(invest|investment advice|financial planning|tax advice)\b",
                r"\b(buy stocks|sell stocks|financial recommendation)\b",
                r"\b(guaranteed returns|risk-free investment)\b"
            ],
            "personal_information": [
                r"\b(social security|ssn|credit card|password|pin)\b",
                r"\b(bank account|routing number|personal details)\b",
                r"\b(share your|provide your|give me your)\b.*\b(password|pin|ssn)\b"
            ],
            "inappropriate_tone": [
                r"\b(stupid|dumb|idiot|moron|ridiculous)\b",
                r"\b(obviously|clearly you don't|you should know)\b",
                r"\b(that's wrong|you're wrong|bad idea)\b"
            ],
            "unauthorized_promises": [
                r"\b(we will|I will|promise to|commit to)\b.*\b(refund|discount|upgrade)\b",
                r"\b(special deal|exclusive offer|one-time)\b",
                r"\b(contact you|call you|email you)\b"
            ]
        }
    
    async def _initialize_default_policies(self):
        """Initialize default policy rules."""
        
        try:
            # Prohibited claims policy
            self.add_policy_rule(PolicyRule(
                rule_id="no_guarantees",
                name="No Absolute Guarantees",
                description="Avoid making absolute guarantees or claims of certainty",
                rule_type="must_not",
                patterns=self.consistency_patterns["prohibited_claims"],
                category="claims",
                severity=ViolationSeverity.HIGH
            ))
            
            # Medical/Legal advice policy
            self.add_policy_rule(PolicyRule(
                rule_id="no_medical_legal",
                name="No Medical or Legal Advice",
                description="Do not provide medical or legal advice",
                rule_type="must_not",
                patterns=self.consistency_patterns["medical_legal_advice"],
                category="advice",
                severity=ViolationSeverity.CRITICAL
            ))
            
            # Financial advice policy
            self.add_policy_rule(PolicyRule(
                rule_id="no_financial_advice",
                name="No Financial Advice",
                description="Do not provide specific financial or investment advice",
                rule_type="must_not",
                patterns=self.consistency_patterns["financial_advice"],
                category="advice",
                severity=ViolationSeverity.HIGH
            ))
            
            # Personal information policy
            self.add_policy_rule(PolicyRule(
                rule_id="no_personal_info",
                name="No Personal Information Requests",
                description="Do not request sensitive personal information",
                rule_type="must_not",
                patterns=self.consistency_patterns["personal_information"],
                category="privacy",
                severity=ViolationSeverity.CRITICAL
            ))
            
            # Tone policy
            self.add_policy_rule(PolicyRule(
                rule_id="professional_tone",
                name="Maintain Professional Tone",
                description="Maintain a professional and respectful tone",
                rule_type="must_not",
                patterns=self.consistency_patterns["inappropriate_tone"],
                category="tone",
                severity=ViolationSeverity.MEDIUM
            ))
            
            # Unauthorized promises policy
            self.add_policy_rule(PolicyRule(
                rule_id="no_unauthorized_promises",
                name="No Unauthorized Promises",
                description="Do not make promises or commitments without authorization",
                rule_type="must_not",
                patterns=self.consistency_patterns["unauthorized_promises"],
                category="commitments",
                severity=ViolationSeverity.HIGH
            ))
            
            self.logger.info("Default policy rules initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize default policies: {e}")
    
    def add_policy_rule(self, rule: PolicyRule):
        """Add a new policy rule."""
        
        self.policy_rules[rule.rule_id] = rule
        self.logger.info(f"Policy rule added: {rule.rule_id} - {rule.name}")
    
    def remove_policy_rule(self, rule_id: str) -> bool:
        """Remove a policy rule."""
        
        if rule_id in self.policy_rules:
            del self.policy_rules[rule_id]
            self.logger.info(f"Policy rule removed: {rule_id}")
            return True
        return False
    
    async def validate_consistency(self, 
                                 response_content: str, 
                                 context: Optional[Dict[str, Any]] = None,
                                 validation_level: ConsistencyLevel = None,
                                 response_id: str = None) -> ConsistencyReport:
        """Perform comprehensive consistency validation."""
        
        start_time = datetime.now()
        
        try:
            validation_level = validation_level or self.validation_settings["default_level"]
            response_id = response_id or f"resp_{hash(response_content)}"
            
            report = ConsistencyReport(
                response_id=response_id,
                response_content=response_content,
                validation_level=validation_level
            )
            
            # Check policy compliance
            policy_violations = await self._check_policy_compliance(
                response_content, context, validation_level
            )
            report.violations.extend(policy_violations)
            
            # Check factual consistency
            factual_violations = await self._check_factual_consistency(
                response_content, context
            )
            report.violations.extend(factual_violations)
            
            # Check tone consistency
            tone_violations = await self._check_tone_consistency(
                response_content, context
            )
            report.violations.extend(tone_violations)
            
            # Calculate scores
            await self._calculate_consistency_scores(report)
            
            # Generate recommendations
            await self._generate_recommendations(report)
            
            # Set processing time
            report.processing_time = (datetime.now() - start_time).total_seconds()
            report.rules_checked = len(self.policy_rules)
            
            # Cache report
            self.validation_cache[response_id] = report
            
            return report
            
        except Exception as e:
            self.logger.error(f"Consistency validation failed: {e}")
            return ConsistencyReport(
                response_id=response_id or "error",
                response_content=response_content,
                validation_level=validation_level or ConsistencyLevel.MODERATE,
                is_consistent=False,
                overall_score=0.0
            )
    
    async def _check_policy_compliance(self, 
                                     response_content: str, 
                                     context: Optional[Dict[str, Any]],
                                     validation_level: ConsistencyLevel) -> List[ConsistencyViolation]:
        """Check compliance with policy rules."""
        
        violations = []
        response_lower = response_content.lower()
        
        for rule in self.policy_rules.values():
            if not rule.active:
                continue
            
            # Check if rule applies to current context
            if not await self._rule_applies_to_context(rule, context):
                continue
            
            # Check rule patterns
            for pattern in rule.patterns:
                matches = list(re.finditer(pattern, response_lower, re.IGNORECASE))
                
                for match in matches:
                    violation = ConsistencyViolation(
                        violation_id=f"policy_{rule.rule_id}_{len(violations)}",
                        violation_type=ViolationType.POLICY_VIOLATION,
                        severity=rule.severity,
                        rule_violated=rule.rule_id,
                        description=f"Policy violation: {rule.description}",
                        evidence=match.group(),
                        response_segment=response_content[max(0, match.start()-20):match.end()+20],
                        position_start=match.start(),
                        position_end=match.end(),
                        suggested_fix=await self._generate_policy_fix(rule, match.group())
                    )
                    violations.append(violation)
        
        return violations
    
    async def _check_factual_consistency(self, 
                                       response_content: str, 
                                       context: Optional[Dict[str, Any]]) -> List[ConsistencyViolation]:
        """Check for factual consistency issues."""
        
        violations = []
        
        # Check for contradictory statements within the response
        contradictions = await self._detect_internal_contradictions(response_content)
        for contradiction in contradictions:
            violation = ConsistencyViolation(
                violation_id=f"factual_contradiction_{len(violations)}",
                violation_type=ViolationType.FACTUAL_INCONSISTENCY,
                severity=ViolationSeverity.HIGH,
                rule_violated="factual_consistency",
                description="Internal contradiction detected in response",
                evidence=contradiction["evidence"],
                suggested_fix="Review and resolve contradictory statements"
            )
            violations.append(violation)
        
        # Check for unsupported claims
        unsupported_claims = await self._detect_unsupported_claims(response_content, context)
        for claim in unsupported_claims:
            violation = ConsistencyViolation(
                violation_id=f"unsupported_claim_{len(violations)}",
                violation_type=ViolationType.UNAUTHORIZED_CLAIM,
                severity=ViolationSeverity.MEDIUM,
                rule_violated="supported_claims",
                description="Claim made without sufficient evidence",
                evidence=claim["text"],
                suggested_fix="Provide source or qualify the statement"
            )
            violations.append(violation)
        
        return violations
    
    async def _check_tone_consistency(self, 
                                    response_content: str, 
                                    context: Optional[Dict[str, Any]]) -> List[ConsistencyViolation]:
        """Check for tone consistency issues."""
        
        violations = []
        
        # Check for tone shifts within response
        tone_issues = await self._detect_tone_inconsistencies(response_content)
        for issue in tone_issues:
            violation = ConsistencyViolation(
                violation_id=f"tone_inconsistency_{len(violations)}",
                violation_type=ViolationType.TONE_INCONSISTENCY,
                severity=ViolationSeverity.LOW,
                rule_violated="tone_consistency",
                description="Inconsistent tone detected in response",
                evidence=issue["evidence"],
                suggested_fix="Maintain consistent professional tone throughout"
            )
            violations.append(violation)
        
        return violations

    async def _rule_applies_to_context(self, rule: PolicyRule, context: Optional[Dict[str, Any]]) -> bool:
        """Check if a rule applies to the current context."""

        if not context:
            return True

        # Check domain restrictions
        if rule.applies_to_domains:
            current_domain = context.get("domain", "general")
            if current_domain not in rule.applies_to_domains:
                return False

        # Check customer tier restrictions
        if rule.customer_tiers:
            customer_tier = context.get("customer_tier", "standard")
            if customer_tier not in rule.customer_tiers:
                return False

        return True

    async def _generate_policy_fix(self, rule: PolicyRule, violation_text: str) -> str:
        """Generate suggested fix for policy violation."""

        fixes = {
            "no_guarantees": "Consider using 'typically', 'usually', or 'in most cases' instead",
            "no_medical_legal": "Recommend consulting with a qualified professional",
            "no_financial_advice": "Suggest speaking with a financial advisor",
            "no_personal_info": "Remove request for sensitive information",
            "professional_tone": "Rephrase using professional language",
            "no_unauthorized_promises": "Qualify statement or remove commitment"
        }

        return fixes.get(rule.rule_id, "Review and revise according to policy guidelines")

    async def _detect_internal_contradictions(self, response_content: str) -> List[Dict[str, Any]]:
        """Detect contradictory statements within the response."""

        contradictions = []

        # Simple contradiction patterns
        contradiction_patterns = [
            (r'\b(yes|true|correct)\b', r'\b(no|false|incorrect)\b'),
            (r'\b(always|never)\b', r'\b(sometimes|occasionally)\b'),
            (r'\b(all|every)\b', r'\b(some|few|none)\b'),
            (r'\b(possible|can)\b', r'\b(impossible|cannot)\b'),
            (r'\b(required|must)\b', r'\b(optional|may)\b')
        ]

        sentences = re.split(r'[.!?]+', response_content)

        for i, sentence1 in enumerate(sentences):
            for j, sentence2 in enumerate(sentences[i+1:], i+1):
                for pos_pattern, neg_pattern in contradiction_patterns:
                    if (re.search(pos_pattern, sentence1, re.IGNORECASE) and
                        re.search(neg_pattern, sentence2, re.IGNORECASE)):
                        contradictions.append({
                            "evidence": f"Sentence {i+1}: {sentence1.strip()} | Sentence {j+1}: {sentence2.strip()}",
                            "type": "logical_contradiction"
                        })

        return contradictions

    async def _detect_unsupported_claims(self,
                                       response_content: str,
                                       context: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect claims that lack sufficient support."""

        unsupported_claims = []

        # Patterns that indicate strong claims
        strong_claim_patterns = [
            r'\b(studies show|research proves|experts agree)\b',
            r'\b(it is proven|scientific fact|established that)\b',
            r'\b(statistics show|data indicates|evidence suggests)\b'
        ]

        for pattern in strong_claim_patterns:
            matches = re.finditer(pattern, response_content, re.IGNORECASE)
            for match in matches:
                # Check if there's a source citation nearby
                context_window = response_content[max(0, match.start()-50):match.end()+100]
                if not re.search(r'\b(source|according to|from|reference)\b', context_window, re.IGNORECASE):
                    unsupported_claims.append({
                        "text": match.group(),
                        "context": context_window,
                        "type": "unsupported_claim"
                    })

        return unsupported_claims

    async def _detect_tone_inconsistencies(self, response_content: str) -> List[Dict[str, Any]]:
        """Detect tone inconsistencies within the response."""

        tone_issues = []

        # Formal vs informal patterns
        formal_patterns = [
            r'\b(furthermore|moreover|consequently|therefore)\b',
            r'\b(please be advised|kindly note|we recommend)\b'
        ]

        informal_patterns = [
            r'\b(yeah|yep|nope|gonna|wanna)\b',
            r'\b(hey|hi there|cool|awesome)\b'
        ]

        has_formal = any(re.search(pattern, response_content, re.IGNORECASE)
                        for pattern in formal_patterns)
        has_informal = any(re.search(pattern, response_content, re.IGNORECASE)
                          for pattern in informal_patterns)

        if has_formal and has_informal:
            tone_issues.append({
                "evidence": "Mixed formal and informal language detected",
                "type": "tone_mixing"
            })

        return tone_issues

    async def _calculate_consistency_scores(self, report: ConsistencyReport):
        """Calculate consistency scores for the report."""

        # Count violations by type and severity
        policy_violations = [v for v in report.violations if v.violation_type == ViolationType.POLICY_VIOLATION]
        factual_violations = [v for v in report.violations if v.violation_type == ViolationType.FACTUAL_INCONSISTENCY]
        tone_violations = [v for v in report.violations if v.violation_type == ViolationType.TONE_INCONSISTENCY]

        # Calculate individual scores
        report.policy_compliance_score = await self._calculate_policy_score(policy_violations)
        report.factual_consistency_score = await self._calculate_factual_score(factual_violations)
        report.tone_consistency_score = await self._calculate_tone_score(tone_violations)

        # Calculate overall score
        report.overall_score = (
            report.policy_compliance_score * 0.5 +
            report.factual_consistency_score * 0.3 +
            report.tone_consistency_score * 0.2
        )

        # Determine if consistent
        critical_violations = [v for v in report.violations if v.severity == ViolationSeverity.CRITICAL]
        high_violations = [v for v in report.violations if v.severity == ViolationSeverity.HIGH]

        report.is_consistent = (
            len(critical_violations) == 0 and
            len(high_violations) <= 1 and
            report.overall_score >= 0.7
        )

    async def _calculate_policy_score(self, violations: List[ConsistencyViolation]) -> float:
        """Calculate policy compliance score."""

        if not violations:
            return 1.0

        # Weight violations by severity
        severity_weights = {
            ViolationSeverity.CRITICAL: 1.0,
            ViolationSeverity.HIGH: 0.7,
            ViolationSeverity.MEDIUM: 0.4,
            ViolationSeverity.LOW: 0.2,
            ViolationSeverity.INFO: 0.1
        }

        total_penalty = sum(severity_weights.get(v.severity, 0.5) for v in violations)
        max_penalty = len(violations) * 1.0  # Assuming worst case all critical

        score = 1.0 - (total_penalty / max(max_penalty, 1.0))
        return max(0.0, score)

    async def _calculate_factual_score(self, violations: List[ConsistencyViolation]) -> float:
        """Calculate factual consistency score."""

        if not violations:
            return 1.0

        # Factual violations are serious
        penalty_per_violation = 0.3
        total_penalty = len(violations) * penalty_per_violation

        score = 1.0 - total_penalty
        return max(0.0, score)

    async def _calculate_tone_score(self, violations: List[ConsistencyViolation]) -> float:
        """Calculate tone consistency score."""

        if not violations:
            return 1.0

        # Tone violations are less critical
        penalty_per_violation = 0.1
        total_penalty = len(violations) * penalty_per_violation

        score = 1.0 - total_penalty
        return max(0.0, score)

    async def _generate_recommendations(self, report: ConsistencyReport):
        """Generate recommendations based on validation results."""

        # Check if review is required
        critical_violations = [v for v in report.violations if v.severity == ViolationSeverity.CRITICAL]
        high_violations = [v for v in report.violations if v.severity == ViolationSeverity.HIGH]

        report.requires_review = (
            len(critical_violations) > 0 or
            len(high_violations) > 2 or
            report.overall_score < 0.6
        )

        # Generate specific recommendations
        if critical_violations:
            report.recommended_actions.append("URGENT: Address critical policy violations before deployment")

        if high_violations:
            report.recommended_actions.append("Review and fix high-severity violations")

        if report.policy_compliance_score < 0.8:
            report.recommended_actions.append("Improve policy compliance")

        if report.factual_consistency_score < 0.8:
            report.recommended_actions.append("Verify factual accuracy and resolve contradictions")

        if report.tone_consistency_score < 0.8:
            report.recommended_actions.append("Maintain consistent professional tone")

        if not report.recommended_actions:
            report.recommended_actions.append("Response meets consistency standards")

    async def get_validation_summary(self, report: ConsistencyReport) -> Dict[str, Any]:
        """Get a summary of validation results."""

        summary = {
            "response_id": report.response_id,
            "is_consistent": report.is_consistent,
            "overall_score": report.overall_score,
            "requires_review": report.requires_review,
            "total_violations": len(report.violations),
            "violation_breakdown": defaultdict(int),
            "severity_breakdown": defaultdict(int),
            "scores": {
                "policy_compliance": report.policy_compliance_score,
                "factual_consistency": report.factual_consistency_score,
                "tone_consistency": report.tone_consistency_score
            },
            "recommendations": report.recommended_actions,
            "processing_time": report.processing_time
        }

        # Count violations by type and severity
        for violation in report.violations:
            summary["violation_breakdown"][violation.violation_type.value] += 1
            summary["severity_breakdown"][violation.severity.value] += 1

        return summary

    async def batch_validate(self, responses: List[Dict[str, Any]]) -> List[ConsistencyReport]:
        """Validate multiple responses in batch."""

        reports = []

        for response_data in responses:
            content = response_data.get("content", "")
            context = response_data.get("context")
            response_id = response_data.get("id")

            report = await self.validate_consistency(content, context, response_id=response_id)
            reports.append(report)

        return reports

    async def get_policy_rules(self) -> List[Dict[str, Any]]:
        """Get list of all policy rules."""

        rules = []

        for rule in self.policy_rules.values():
            rule_data = {
                "rule_id": rule.rule_id,
                "name": rule.name,
                "description": rule.description,
                "rule_type": rule.rule_type,
                "category": rule.category,
                "severity": rule.severity.value,
                "active": rule.active,
                "pattern_count": len(rule.patterns),
                "keyword_count": len(rule.keywords),
                "created_at": rule.created_at.isoformat(),
                "updated_at": rule.updated_at.isoformat()
            }
            rules.append(rule_data)

        return rules

    async def get_validation_statistics(self) -> Dict[str, Any]:
        """Get statistics about validation performance."""

        if not self.validation_cache:
            return {"message": "No validation reports in cache"}

        reports = list(self.validation_cache.values())

        stats = {
            "total_validations": len(reports),
            "consistency_rate": sum(1 for r in reports if r.is_consistent) / len(reports),
            "average_score": sum(r.overall_score for r in reports) / len(reports),
            "review_rate": sum(1 for r in reports if r.requires_review) / len(reports),
            "violation_frequency": defaultdict(int),
            "severity_distribution": defaultdict(int),
            "average_processing_time": sum(r.processing_time for r in reports) / len(reports)
        }

        # Count violations
        for report in reports:
            for violation in report.violations:
                stats["violation_frequency"][violation.violation_type.value] += 1
                stats["severity_distribution"][violation.severity.value] += 1

        return stats

    def clear_cache(self):
        """Clear the validation cache."""
        self.validation_cache.clear()
        self.logger.info("Consistency validation cache cleared")
